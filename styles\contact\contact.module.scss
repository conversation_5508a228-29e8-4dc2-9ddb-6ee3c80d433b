.contactContainer {
  min-height: 100vh;
  padding: 2rem 0;
  padding-top: 6rem;

  .titleInfo {
    padding: 1rem 5rem;
    h1 {
      font-size: 2.3rem;
      text-align: center;
      font-weight: 800;
    }
    p {
      font-size: 1.1rem;
      text-align: center;
    }
  }

  .contactContent {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 2rem;
  }
  .leftContainer {
    position: relative;
    width: 100%;
    background-color: #f6f6f6;
    display: flex;
    justify-content: center;
    align-items: center;

    mask-image: url("/assets/decors/paintBg2.png");
    mask-size: 40rem 50rem; // width height
    mask-repeat: no-repeat;
    mask-position: center;

    -webkit-mask-image: url("/assets/decors/paintBg2.png");
    -webkit-mask-size: 40rem 50rem;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;

    & > div {
      width: 30rem;
      height: 30rem;
    }
  }
  .rightContainer {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .contactForm {
      width: 100%;
      max-width: 500px;
      display: flex;
      flex-direction: column;
      gap: 1rem;

      input,
      textarea {
        padding: 0.75rem 1rem;
        font-size: 1rem;
        border: 1px solid #ccc;
        border-radius: 5px;
      }

      button {
        padding: 0.75rem 1rem;
        font-size: 1rem;
        background-color: #ce3f3f;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;

        &:hover {
          background-color: #aa2525;
        }
      }
    }
  }

  .contactInfoContainer {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 2rem;
    justify-content: center;
    align-items: center;

    & > div {
      display: flex;
      align-items: center;
      gap: 2rem;

      & > div {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .icon {
          font-size: 1.5rem;
          color: #ce3f3f;
        }

        a {
          font-size: 1.5rem;
        }

        p {
          font-size: 1.2rem;
        }
      }
    }
  }
}

@media (max-width: 1600px) {
  .contactContainer {
    .titleInfo {
      padding: 1rem 2rem;
      h1 {
        font-size: 2rem;
      }
      p {
        font-size: 1.05rem;
      }
    }

    .leftContainer {
      padding: 2rem;
      mask-size: 20rem 30rem;
      -webkit-mask-size: 20rem 30rem;

      & > div {
        width: 20rem;
        height: 20rem;
      }
    }

    .rightContainer {
      .contactForm {
        width: 100%;
        max-width: 500px;
        gap: 0.5rem;

        input,
        textarea {
          padding: 0.5rem 1rem;
          font-size: 0.9rem;
          border-radius: 5px;
        }

        button {
          padding: 0.5rem 1rem;
          font-size: 0.9rem;
        }
      }
    }

    .contactInfoContainer {
      & > div {
        & > div {
          .icon {
            font-size: 1.25rem;
            color: #ce3f3f;
          }

          a {
            font-size: 1.15rem;
          }
        }
      }
    }
  }
}

@media (max-width: 850px) {
  .contactContainer {
    min-height: 100vh;
    padding: 2rem 0;
    padding-top: 6rem;

    .titleInfo {
      padding: 1rem;
      h1 {
        font-size: 1.75rem;
      }
      p {
        font-size: 1rem;
      }
    }

    .contactContent {
      display: grid;
      grid-template-columns: 1fr 1fr;
      padding: 2rem;
    }

    .contactInfoContainer {
      padding: 1rem;

      & > div {
        gap: 2rem;

        & > div {
          .icon {
            font-size: 1.25rem;
          }

          a {
            font-size: 1.25rem;
          }
        }
      }
    }
  }
}

@media (max-width: 720px) {
  .contactContainer {
    .contactContent {
      display: flex;
      flex-direction: column-reverse;
      padding: 2rem;
    }

    .contactInfoContainer {
      padding: 1rem;

      & > div {
        gap: 0.3rem;
        flex-direction: column;
        margin-top: -1rem;

        & > div {

          .icon {
            font-size: 1.25rem;
          }

          a {
            font-size: 1.25rem;
          }
        }
      }
    }
  }
}
