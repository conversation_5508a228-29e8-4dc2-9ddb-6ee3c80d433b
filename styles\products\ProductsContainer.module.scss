.productsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .productslistContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 10rem;
    position: relative;

    .decor2 {
      position: absolute;
      right: 0;
      bottom: 0;
      z-index: -1;
    }

    .decor1 {
      position: absolute;
      left: 0;
      top: 0;
      z-index: -9;
    }

    .productTitle {
      h3 {
        text-align: center;
        font-size: 2rem;
        margin-top: 2rem;
        font-weight: 800;
      }
      p {
        font-size: 1.2rem;
        padding: 1rem 0;
      }
    }

    .productCards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      width: 100%;
      padding: 1rem;
      gap: 2rem;
      place-items: center;
      z-index: 99;

      .cardLink {
        text-decoration: none;
        color: inherit;
        display: block;
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-5px);
        }
      }

      .card1 {
        position: relative;

        .decorSpiral {
          position: absolute;
          top: 5rem;
          left: -6rem;
          transform: scaleX(-1);
        }
      }

      h4 {
        font-size: 1.5rem;
        padding: 0rem 1rem;
        text-align: center;
        font-weight: 800;
      }

      p {
        font-size: 1rem;
        font-weight: 800;
        padding: 0 1rem;
        text-align: center;
        margin-bottom: 1rem;
      }

      .viewProductBtn {
        background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 0 1rem 2rem;
        width: calc(100% - 2rem);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 16px rgba(229, 62, 62, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }

      .cardImgContainer {
        position: relative;
        width: 20rem;
        height: 20rem;
        border-radius: 1rem;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .discountBadge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 700;
        color: white;
        z-index: 2;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

        &.nonveg {
          background: #e53e3e;
        }

        &.veg {
          background: #38a169;
        }

        &.powder {
          background: #d69e2e;
        }
      }
    }

    .decor2 {
      position: absolute;
      bottom: 0;
      right: 0;
      z-index: 999;
    }
  }

  .divider {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20vw;
    margin: -2rem auto;
    img {
      height: 2rem;
      opacity: 0.2;
    }
  }
}

// Large Desktop
@media (max-width: 1400px) {
  .productsContainer {
    .productslistContainer {
      .productCards {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;

        .cardImgContainer {
          width: 18rem;
          height: 18rem;
        }

        h4 {
          font-size: 1.3rem;
        }

        p {
          font-size: 1rem;
        }

        .discountBadge {
          padding: 0.4rem 0.8rem;
          font-size: 0.8rem;
        }

        .viewProductBtn {
          padding: 0.6rem 1.2rem;
          font-size: 0.85rem;
        }
      }
    }
  }
}

// Medium Desktop / Laptop
@media (max-width: 1200px) {
  .productsContainer {
    .productslistContainer {
      .productCards {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        gap: 1.5rem;

        .cardImgContainer {
          width: 16rem;
          height: 16rem;
        }

        h4 {
          font-size: 1.2rem;
        }

        p {
          font-size: 0.95rem;
        }

        .discountBadge {
          padding: 0.4rem 0.8rem;
          font-size: 0.8rem;
        }

        .viewProductBtn {
          padding: 0.6rem 1.2rem;
          font-size: 0.8rem;
        }
      }
    }
  }
}

// Tablet
@media (max-width: 900px) {
  .productsContainer {
    .productslistContainer {
      .productCards {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;

        .cardImgContainer {
          width: 15rem;
          height: 15rem;
        }

        h4 {
          font-size: 1.2rem;
        }

        p {
          font-size: 0.95rem;
        }

        .discountBadge {
          padding: 0.35rem 0.7rem;
          font-size: 0.75rem;
        }

        .viewProductBtn {
          padding: 0.6rem 1.2rem;
          font-size: 0.8rem;
        }
      }
    }
  }
}

// Mobile Large
@media (max-width: 680px) {
  .productsContainer {
    .productslistContainer {
      padding: 0.5rem;

      .productTitle {
        h3 {
          font-size: 1.6rem;
        }

        p {
          font-size: 0.85rem;
        }
      }

      .productCards {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        padding: 0.5rem;

        .cardImgContainer {
          width: 12.5rem !important;
          height: 12.5rem !important;
        }

        h4 {
          font-size: 0.9rem;
          padding: 0.5rem;
        }

        p {
          font-size: 0.75rem;
          margin-bottom: 0.5rem;
        }

        .discountBadge {
          padding: 0.25rem 0.5rem;
          font-size: 0.6rem;
          top: 0.4rem;
          right: 0.4rem;
        }

        .viewProductBtn {
          padding: 0.4rem 0.8rem;
          font-size: 0.7rem;
          margin: 0 1rem 1rem;
          width: calc(100% - 2rem);
        }
      }

      .decor2,.decor1 {
        img{
          width: 5rem;
        }
      }
    }
  }
}

// Mobile Small
@media (max-width: 480px) {
  .productsContainer {
    .productslistContainer {
      .productTitle {
        h3 {
          font-size: 1.4rem;
        }

        p {
          font-size: 0.8rem;
        }
      }

      .productCards {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;

        .cardImgContainer {
          width: 10rem !important;
          height: 10rem !important;
        }

        h4 {
          font-size: 0.8rem;
          padding: 0.25rem;
        }

        p {
          font-size: 0.7rem;
          margin-bottom: 0.5rem;
        }

        .discountBadge {
          padding: 0.2rem 0.4rem;
          font-size: 0.55rem;
          top: 0.3rem;
          right: 0.3rem;
        }

        .viewProductBtn {
          padding: 0.35rem 0.7rem;
          font-size: 0.65rem;
          margin: 0 0.75rem 0.75rem;
          width: calc(100% - 1.5rem);
        }
      }
    }
  }
}
