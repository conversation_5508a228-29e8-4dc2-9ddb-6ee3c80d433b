.productsHeroContainer {
  min-height: 100vh;
  background: url("/assets/decors/chefHands.jpg") center/contain no-repeat fixed
    border-box;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 50% 50%;
  position: relative;

  .infoContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    z-index: 99;
    padding: 2rem 8rem;
    & > div {
      z-index: 9;
      display: flex;
      flex-direction: column;
      h2 {
        font-size: 2.2rem;
        font-weight: 900;
        color: var(--black);
        text-align: center;
      }

      h1 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        letter-spacing: 0.25rem;
        line-height: 1.5rem;
        color: #ce0d13;
        font-weight: 800;
      }
      p {
        text-align: center;
        font-size: 1.1rem;
        margin: 1.5rem 0;
        font-weight: 800;
      }
    }
  }
}

.imgContainer {
  position: relative;
  z-index: 9;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brushContainer {
  position: relative;
  top: -3rem;
  width: 90%;
  height: 100%;
  background-color: #f6f6f6;

  mask-image: url("/assets/decors/paintBg.png");
  mask-size: 60rem 90%;
  mask-repeat: no-repeat;
  mask-position: center;

  -webkit-mask-image: url("/assets/decors/paintBg.png");
  -webkit-mask-size: 60rem 90%;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.containerHero::before {
  content: "";
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0);
}

@media (max-width: 1600px) {
  .productsHeroContainer {
    .infoContainer {
      padding: 2rem 4rem;
      align-items: center;
      & > div {
        h2 {
          font-size: 1.5rem;
        }

        h1 {
          font-size: 1.25rem;
          margin-bottom: 0.25rem;
        }
        p {
          font-size: 0.9rem;
          margin: 0.5rem 0;
        }
      }
    }
  }

  .brushContainer {
    mask-size: 60rem 90%;
    -webkit-mask-size: 60rem 90%;
  }
}

@media (max-width: 680px) {
  .productsHeroContainer {
    margin-top: 2rem;
    .infoContainer {
      padding: 1rem;
      & > div {
        h2 {
          font-size: 1.3rem;
        }

        h1 {
          font-size: 1.1rem;
          margin-bottom: 0.25rem;
        }
        p {
          font-size: 0.9rem;
          margin: 0.5rem 0;
        }
      }
    }
  }

  .imgContainer {
    align-items: flex-end;
    justify-content: center;
  }

  .brushContainer {
    mask-size: 40rem 70%;
    -webkit-mask-size: 40rem 70%;
  }
}

@media (max-width: 590px) {
  .productsHeroContainer {
    margin-top: 0rem;
  }
  .brushContainer {
    top: -5rem;
    width: 90%;
    height: 300px;
    mask-size: 35rem 90%;
    -webkit-mask-size: 35rem 90%;
  }
}

@media (max-width: 470px) {
  .productsHeroContainer {
    margin-top: 5rem;
  }
}

@media (max-width: 440px) {
  .brushContainer {
    top: -5rem;
    width: 90%;
    height: 300px;
    mask-size: 33rem 75%;
    -webkit-mask-size: 33rem 75%;
  }
}
