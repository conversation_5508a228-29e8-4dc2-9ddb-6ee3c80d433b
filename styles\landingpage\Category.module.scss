.categoryContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 0rem;
  position: relative;
  background: url("/assets/spiceAssests/spicesBg.jpg") center/cover no-repeat
    fixed border-box;

  h1 {
    font-size: 2.5rem;
    padding: 1rem 0;
    z-index: 99;
    color: var(--white);
  }

  .categoryCardsContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    gap: 10rem;
    margin-top: 2rem;
    width: 90%;
  }

  .categoryCard {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    z-index: 99;

    .imgContainer {
      position: relative;
      width: 25rem;
      height: 30rem;
      border-radius: 1rem;
      overflow: hidden;
    }

    h3 {
      font-size: 1.5rem;
      z-index: 99;
      font-weight: 800;
    }
  }

  img {
    width: 100%;
    height: 100%;
  }

  .decor {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
  }
}

.favContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 8rem;
  position: relative;

  .favTitle {
    h3 {
      text-align: center;
      font-size: 2rem;
      margin-top: 2rem;
      font-weight: 800;
    }
    p {
      font-size: 1.2rem;
      padding: 1rem 0;
    }
  }

  .favCards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    width: 100%;
    padding: 1rem;
    place-items: center;

    .favCard:nth-child(5) {
      grid-column: 2;
    }

    .favCard:nth-child(6) {
      grid-column: 3;
    }

    .favCard1 {
      position: relative;

      .decorSpiral {
        position: absolute;
        top: 5rem;
        left: -6rem;
        transform: scaleX(-1);
      }
    }

    h4 {
      font-size: 1.5rem;
      padding: 1rem;
      text-align: center;
      font-weight: 800;
    }

    .favImgContainer {
      position: relative;
      width: 20rem;
      height: 20rem;
      border-radius: 1rem;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .decor2 {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 999;
  }
}

.categoryContainer::before {
  content: "";
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.1);
}

@media (max-width: 1600px) {
  .categoryContainer {
    padding: 2rem 0rem;

    h1 {
      font-size: 2rem;
      padding: 0rem 0;
    }

    .categoryCard {
      gap: 1rem;

      .imgContainer {
        width: 20rem;
        height: 25rem;
        border-radius: 1rem;
      }

      h3 {
        font-size: 1.5rem;
      }
    }

    .decor {
      width: 8rem;
    }
  }

  .favContainer {
    padding: 1rem 5rem;

    .favTitle {
      h3 {
        font-size: 2rem;
        margin-top: 1rem;
      }
      p {
        font-size: 1.1rem;
        padding: 0.25rem 0;
      }
    }

    .favCards {
      grid-template-columns: repeat(3, 1fr);

      h4 {
        font-size: 1.25rem;
        padding: 0.5rem;
      }

      .favImgContainer {
        width: 17.5rem;
        height: 17.5rem;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

@media (max-width: 980px) {
  .categoryContainer {
    padding: 2rem 0rem;

    .categoryCard {
      .imgContainer {
        width: 15rem;
        height: 15rem;
      }

      h3 {
        font-size: 1.25rem;
      }
    }

    .decor {
      width: 8rem;
    }
  }

  .favContainer {
    padding: 1rem 2rem;

    .favTitle {
      h3 {
        font-size: 1.5rem;
      }
      p {
        font-size: 1rem;
      }
    }

    .favCards {
      grid-template-columns: repeat(3, 1fr);

      h4 {
        font-size: 1.25rem;
      }

      .favImgContainer {
        width: 12.5rem;
        height: 12.5rem;
      }
    }

    .decor2 {
      width: 5rem;
    }
  }
}

@media (max-width: 750px) {
  .categoryContainer {
    padding: 4rem 0rem;

    h1 {
      font-size: 2rem;
    }

    .categoryCardsContainer {
      gap: 1rem;
      margin-top: 1rem;
    }

    .categoryCard {
      .imgContainer {
        width: 15rem;
        height: 15rem;
      }

      h3 {
        font-size: 1.25rem;
      }
    }

    .decor {
      width: 5rem;
    }
  }
}

@media (max-width: 685px) {
  .favContainer {
    .favCards {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

@media (max-width: 550px) {
  .categoryContainer {
    h1 {
      font-size: 1.5rem;
    }
    .categoryCard {
      .imgContainer {
        width: 10rem;
        height: 10rem;
      }
    }

    .decor {
      width: 5rem;
    }
  }
}

@media (max-width: 480px) {
  .favContainer {

    .favCards {
      .favImgContainer {
        width: 9.5rem;
        height: 9.5rem;
      }
    }
  }
}
