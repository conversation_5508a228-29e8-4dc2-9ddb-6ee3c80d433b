.topSlider {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  height: 50px;
  overflow: hidden;
}

.slideTrack {
  position: relative;
  width: 100%;
  height: 100%;
}

.slideContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slideContent {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  text-align: center;
}

.icon {
  font-size: 1.1rem;
  display: flex;
  align-items: center;
}

.text {
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.indicators {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 0.5rem;
  align-items: center;
  z-index: 10;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.5);
  background-color: transparent;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    background-color: white;
    transform: scale(1.2);
  }

  &:hover {
    background-color: rgba(255, 255, 255, 0.7);
  }
}

/* Remove the old slideIn animation as we're using transform-based sliding */

/* Responsive Design */
@media (max-width: 768px) {
  .topSlider {
    height: 45px;
  }

  .slideContent {
    gap: 0.5rem;
    font-size: 0.8rem;
    padding: 0 1rem;
  }

  .icon {
    font-size: 1rem;
  }

  .text {
    font-size: 0.8rem;
  }

  .indicators {
    right: 0.5rem;
    gap: 0.3rem;
  }

  .indicator {
    width: 6px;
    height: 6px;
  }
}

@media (max-width: 480px) {
  .topSlider {
    height: 40px;
  }

  .slideContent {
    font-size: 0.75rem;
    gap: 0.4rem;
  }

  .icon {
    font-size: 0.9rem;
  }

  .text {
    font-size: 0.75rem;
  }

  .indicators {
    display: none; /* Hide indicators on very small screens */
  }
}
