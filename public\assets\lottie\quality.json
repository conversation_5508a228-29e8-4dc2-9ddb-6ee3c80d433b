{"v": "5.7.6", "fr": 30, "ip": 0, "op": 150, "w": 500, "h": 500, "nm": "9 - Award", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 2, "ty": 4, "nm": "Part Up", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 0, "s": [50.001, -62, 0], "to": [0, 3.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 38, "s": [50.001, -42, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 75, "s": [50.001, -62, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 113, "s": [50.001, -42, 0], "to": [0, 0, 0], "ti": [0, 3.333, 0]}, {"t": 150, "s": [50.001, -62, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [118.712, 63.563, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[118.462, 5.385], [-118.462, 5.385], [-118.462, -5.385], [118.462, -5.385]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.372999991623, 0.4, 0.458999992819, 1], "ix": 4, "x": "var $bm_rt;\ntry {\n    $bm_rt = thisComp.layer('Controller').effect('Color')('ADBE Color Control-0001');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [118.712, 5.635], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transformer "}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[43.077, 41.233], [0, 60.62], [-43.077, 41.233], [-43.077, -60.62], [43.077, -60.62]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.769000004787, 1, 1], "ix": 4, "x": "var $bm_rt;\ntry {\n    $bm_rt = thisComp.layer('Controller').effect('Color 2')('ADBE Color Control-0001');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [118.711, 66.255], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transformer "}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[107.693, 12.154], [0.001, 60.62], [-107.693, 12.154], [-107.693, -60.62], [107.693, -60.62]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.289999988032, 0.647000002394, 0.811999990426, 1], "ix": 4, "x": "var $bm_rt;\ntry {\n    $bm_rt = thisComp.layer('Controller').effect('Color 3')('ADBE Color Control-0001');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [118.711, 66.255], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transformer "}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Part Ellipse", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [50, 122.692, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [96.923, 96.923, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.5, 0.5, 0.5], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 0, "s": [85, 85, 100]}, {"i": {"x": [0.5, 0.5, 0.5], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 38, "s": [100, 100, 100]}, {"i": {"x": [0.5, 0.5, 0.5], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 75, "s": [85, 85, 100]}, {"i": {"x": [0.5, 0.5, 0.5], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 113, "s": [100, 100, 100]}, {"t": 150, "s": [85, 85, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 31.225], [31.225, 0], [0, -31.225], [-31.226, 0]], "o": [[0, -31.225], [-31.226, 0], [0, 31.225], [31.225, 0]], "v": [[56.539, 0], [0.001, -56.538], [-56.539, 0], [0.001, 56.538]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.941000007181, 0.368999974868, 0.337000020345, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    $bm_rt = thisComp.layer('Controller').effect('Color 4')('ADBE Color Control-0001');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16.154, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.862999949736, 0.411999990426, 1], "ix": 4, "x": "var $bm_rt;\ntry {\n    $bm_rt = thisComp.layer('Controller').effect('Color 5')('ADBE Color Control-0001');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [96.923, 96.923], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transformer "}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Part Star", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 38, "s": [-23]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 75, "s": [0]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 113, "s": [-23]}, {"t": 150, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [50, 122.692, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [103.94, 103.941, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.709, 2.51], [0, 0], [-2.007, -4.367], [0, 0], [-3.463, 1.283], [0, 0], [-0.183, -4.802], [0, 0], [-3.69, -0.14], [0, 0], [1.669, -4.507], [0, 0], [-3.356, -1.542], [0, 0], [3.266, -3.525], [0, 0], [-2.51, -2.709], [0, 0], [4.367, -2.007], [0, 0], [-1.282, -3.463], [0, 0], [4.802, -0.182], [0, 0], [0.141, -3.69], [0, 0], [4.507, 1.669], [0, 0], [1.542, -3.355], [0, 0], [3.525, 3.267], [0, 0], [2.709, -2.51], [0, 0], [2.007, 4.366], [0, 0], [3.463, -1.283], [0, 0], [0.183, 4.802], [0, 0], [3.69, 0.14], [0, 0], [-1.668, 4.507], [0, 0], [3.356, 1.542], [0, 0], [-3.266, 3.525], [0, 0], [2.51, 2.708], [0, 0], [-4.367, 2.006], [0, 0], [1.282, 3.463], [0, 0], [-4.803, 0.182], [0, 0], [-0.141, 3.69], [0, 0], [-4.507, -1.669], [0, 0], [-1.541, 3.355], [0, 0], [-3.525, -3.267], [0, 0]], "o": [[0, 0], [3.525, -3.267], [0, 0], [1.542, 3.355], [0, 0], [4.507, -1.669], [0, 0], [0.141, 3.69], [0, 0], [4.802, 0.182], [0, 0], [-1.282, 3.463], [0, 0], [4.367, 2.006], [0, 0], [-2.51, 2.708], [0, 0], [3.266, 3.525], [0, 0], [-3.356, 1.542], [0, 0], [1.669, 4.507], [0, 0], [-3.69, 0.14], [0, 0], [-0.183, 4.802], [0, 0], [-3.463, -1.283], [0, 0], [-2.007, 4.366], [0, 0], [-2.709, -2.51], [0, 0], [-3.525, 3.267], [0, 0], [-1.541, -3.355], [0, 0], [-4.507, 1.669], [0, 0], [-0.141, -3.69], [0, 0], [-4.803, -0.182], [0, 0], [1.282, -3.463], [0, 0], [-4.367, -2.007], [0, 0], [2.51, -2.709], [0, 0], [-3.266, -3.525], [0, 0], [3.356, -1.542], [0, 0], [-1.668, -4.507], [0, 0], [3.69, -0.14], [0, 0], [0.183, -4.802], [0, 0], [3.463, 1.283], [0, 0], [2.007, -4.367], [0, 0], [2.709, 2.51]], "v": [[4.801, -91.766], [14.144, -100.423], [25.363, -98.191], [30.682, -86.617], [39.553, -82.943], [51.498, -87.366], [61.009, -81.011], [61.493, -68.283], [68.283, -61.493], [81.012, -61.008], [87.367, -51.497], [82.943, -39.552], [86.618, -30.682], [98.192, -25.363], [100.424, -14.143], [91.766, -4.801], [91.766, 4.801], [100.424, 14.145], [98.192, 25.363], [86.618, 30.682], [82.943, 39.552], [87.367, 51.497], [81.012, 61.008], [68.283, 61.493], [61.493, 68.283], [61.009, 81.011], [51.498, 87.367], [39.553, 82.943], [30.682, 86.617], [25.363, 98.192], [14.144, 100.423], [4.801, 91.766], [-4.801, 91.766], [-14.144, 100.423], [-25.363, 98.192], [-30.682, 86.617], [-39.553, 82.943], [-51.498, 87.367], [-61.009, 81.011], [-61.493, 68.283], [-68.283, 61.493], [-81.011, 61.008], [-87.367, 51.497], [-82.943, 39.552], [-86.618, 30.682], [-98.192, 25.363], [-100.424, 14.145], [-91.766, 4.801], [-91.766, -4.801], [-100.424, -14.143], [-98.192, -25.363], [-86.618, -30.682], [-82.943, -39.552], [-87.367, -51.497], [-81.011, -61.008], [-68.283, -61.493], [-61.493, -68.283], [-61.009, -81.011], [-51.498, -87.366], [-39.553, -82.943], [-30.682, -86.617], [-25.363, -98.191], [-14.144, -100.423], [-4.801, -91.766]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.728999956916, 0.301999978458, 1], "ix": 4, "x": "var $bm_rt;\ntry {\n    $bm_rt = thisComp.layer('Controller').effect('Color 6')('ADBE Color Control-0001');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [103.94, 103.941], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transformer "}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 3, "nm": "Move", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [-6]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 56, "s": [6]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [-6]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 131, "s": [6]}, {"t": 150, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 0, "s": [250, 220, 0], "to": [0, 10, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 38, "s": [250, 280, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 75, "s": [250, 220, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 113, "s": [250, 280, 0], "to": [0, 0, 0], "ti": [0, 10, 0]}, {"t": 150, "s": [250, 220, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [50, 50, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 150, "st": 0, "bm": 0}], "markers": []}