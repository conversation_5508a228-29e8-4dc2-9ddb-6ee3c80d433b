.containerHero {
  min-height: 100vh;
  background: url("/assets/decors/chefHands.jpg") center/contain no-repeat fixed
    border-box;
  display: grid;
  grid-template-columns: 40% 60%;
  position: relative;

  .infoContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    z-index: 99;
    & > div {
      z-index: 9;
      display: flex;
      flex-direction: column;

      h2 {
        font-size: 4rem;
        font-weight: 900;
        color: var(--black);
        text-align: center;
      }

      h3 {
        display: none;
      }

      h1 {
        font-size: 2.5rem;
        font-weight: 800;
        letter-spacing: 0.25rem;
        line-height: 1.5rem;
        color: #ce0d13;
      }
      p {
        text-align: center;
        font-size: 1.5rem;
        font-weight: 800;
      }
    }
  }
}

.orderBtn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: 5px solid var(--head);
  background-color: var(--head);
  padding-left: 0.2rem;
  padding-right: 0.7rem;
  border-radius: 2rem;
  cursor: pointer;

  .button {
    font-size: 1rem;
    font-weight: 700;
    background-color: var(--white);
    padding: 0.5rem 1.5rem;
    border-radius: 2rem;
    border: none;
  }

  .span {
    width: 2rem;
    height: 100%;
    border-radius: 2rem;
    display: flex;
    align-items: center;
  }

  svg {
    width: 1.5rem;
    height: 1.5rem;
  }
}

.imgContainer {
  position: relative;
  height: 80vh;
  top: 5rem;
  width: 60vw;
  right: 5rem;
  z-index: 9;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  .img1,
  .img2 {
    position: relative;
    width: 30rem;
    height: 20rem;
    z-index: 99;
    border-radius: 2rem;
  }
  .img1 {
    right: 8rem;
  }
  .img2 {
    left: 8rem;
  }
}

.brushContainer {
  position: relative;
  width: 90%; // adjust as needed
  height: 600px;
  background-color: #f6f6f6;
  display: flex;
  justify-content: center;
  align-items: center;

  mask-image: url("/assets/decors/paintBg.png");
  mask-size: cover;
  mask-repeat: no-repeat;
  mask-position: center;

  -webkit-mask-image: url("/assets/decors/paintBg.png");
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.containerHero::before {
  content: "";
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0);
}

@media (max-width: 1600px) {
  .containerHero {
    grid-template-columns: 45% 55%;

    .infoContainer {
      & > div {
        h2 {
          font-size: 2.5rem;
        }

        h1 {
          font-size: 1.75rem;
          letter-spacing: 0.15rem;
          color: #ce0d13;
        }
        p {
          font-size: 1rem;
        }
      }
    }

    .orderBtn {
      border: 5px solid var(--head);
      padding-left: 0.2rem;
      padding-right: 0.7rem;

      .button {
        font-size: 0.9rem;
        padding: 0.25rem 1.5rem;
      }

      svg {
        width: 1.25rem;
        height: 1.25rem;
      }
    }
  }

  .brushContainer {
    width: 90%;
    height: 100%;
    mask-size: 60rem 30rem;
    -webkit-mask-size: 60rem 30rem;

    .imageWrapper {
      position: relative;
      width: 100% !important;
      height: 100% !important;
    }
  }
}

@media (max-width: 1300px) {
  .brushContainer {
    height: 300px;

    mask-size: 40rem 20rem;
    -webkit-mask-size: 40rem 20rem;
  }
}

@media (max-width: 950px) {
  .containerHero {
    grid-template-columns: 1fr;
    grid-template-rows: 40% 6%;
    gap: 1rem;

    .infoContainer {
      margin-top: 0rem;
      justify-content: flex-end;
      & > div {
        h2 {
          font-size: 2rem;
          display: none;
        }

        h3 {
          display: block;
          font-size: 2rem;
          text-align: center;
        }

        h1 {
          font-size: 1.5rem;
          margin-bottom: -1rem;
        }
      }
    }
  }

  .imgContainer {
    align-items: flex-start;
    width: 100%;
    top: 0;
    right: 0;
  }
}

@media (max-width: 540px) {
  .containerHero {
    .infoContainer {
      margin-top: 0rem;
      & > div {
        h3 {
          display: block;
          font-size: 1.5rem;
        }

        h1 {
          font-size: 1.25rem;
        }
      }
    }
  }

  .brushContainer {
    height: 300px;
    mask-size: 40rem 15rem;
    -webkit-mask-size: 40rem 15rem;
  }
}

@media (max-width: 450px) {
  .containerHero {
    margin-top: 2rem;
  }
}

@media (max-width: 440px) {
  .brushContainer {
    height: 300px;
    mask-size: 28rem 13rem;
    -webkit-mask-size: 28rem 13rem;
  }
}

