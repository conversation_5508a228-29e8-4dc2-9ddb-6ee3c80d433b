.footerContainer {
  padding: 3rem 4rem;
  padding-top: 5rem;
  background-color: rgba(245, 244, 244, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  position: relative;

  .footerContent {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    gap: 15rem;
    width: 100%;
  }

  .mobileInfo {
    display: none;
  }

  img {
    z-index: 999;
    padding-bottom: 1rem;
  }

  .footerLinksContainer {
    & > div {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;

      h2 {
        font-size: 1.1rem;
        font-weight: 600;
        padding-bottom: 1rem;
        color: var(--head);
        z-index: 999;
      }

      a {
        color: var(--para);
        transition: 0.25s all;
        z-index: 999;
      }

      a:hover {
        color: var(--black);
      }
    }
  }

  .contactItem {
    display: flex;
    flex-direction: row !important;
    align-items: center;
    gap: 0.5rem;

    .icon {
      font-size: 1rem;
      color: var(--head);
    }
  }

  .socialIcons {
    display: flex;
    flex-direction: row !important;
    gap: 1rem;
    align-items: center;

    .socialIcon {
      font-size: 1.5rem;
      color: var(--head);
      transition: 0.25s all;

      &:hover {
        color: var(--black);
        transform: scale(1.1);
      }
    }
  }

  .copyrightSection {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    p {
      color: var(--para);
      font-size: 0.9rem;
    }
  }
}

.footerContainer::before {
  content: "";
  position: absolute;
  width: 85vw;
  height: 0.1rem;
  top: 0;
  background-color: var(--black);
}

@media (max-width: 1600px) {
  .footerContainer {
    padding: 3rem;

    .footerContent {
      justify-content: space-around;
      gap: 1rem;
    }

    .footerLinksContainer {
      & > div {
        img {
          z-index: 999;
          padding-bottom: 2rem;
        }

        h2 {
          font-size: 1.1rem;
        }
      }
    }
  }
}

@media (max-width: 620px) {
  .footerContainer {
    padding: 3rem;

    .footerContent {
      flex-direction: column;
      align-items: center;
      gap: 2rem;
    }

    .desktopInfo {
      display: none;
    }

    .mobileInfo {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      width: 100%;
    }

    img {
      width: 10rem;
    }

    .footerLinksContainer {
      & > div {
        h2 {
          font-size: 1rem;
        }

        a {
          font-size: 0.9rem;
        }
      }
    }

    .contactItem {
      display: flex;
      flex-direction: row !important;
      align-items: center;
      gap: 0.5rem;

      .icon {
        font-size: 1rem;
        color: var(--head);
      }
    }

    .socialIcons {
      .socialIcon {
        font-size: 1.2rem;
      }
    }

    .copyrightSection {

      img {
        width: 3rem;
      }
    }
  }
}
