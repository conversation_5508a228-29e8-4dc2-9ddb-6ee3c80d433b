.chooseContainer {
  display: flex;
  flex-direction: column;
  padding: 4rem 2rem;
  background: url("/assets/spiceAssests/garlic.jpg") center/cover no-repeat
    fixed border-box;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.1);
  }

  .chooseTitle {
    z-index: 99;
    h3 {
      font-size: 1.5rem;
      text-align: center;
      margin-bottom: 1rem;
      font-weight: 800;
    }

    h1 {
      font-size: 2.5rem;
      text-align: center;
      margin-bottom: 1rem;
      color: black;
      font-weight: 800;
    }

    p {
      font-size: 1.3rem;
      text-align: center;
      margin-bottom: 1rem;
      color: black;
    }
  }

  .grid {
    z-index: 999;
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(2, 1fr);
    padding: 2rem 10rem;
  }

  .card {
    z-index: 999;
    background: #fff;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    &:hover {
      transform: translateY(-5px);
    }

    .icon {
      width: 5rem;
      height: 5rem;
    }

    h3 {
      font-size: 1.3rem;
      font-weight: 600;
      color: #333;
      text-align: center;
      margin-bottom: 0.5rem;
    }

    p {
      font-size: 1rem;
      color: #555;
      text-align: center;
    }
  }
}

@media (max-width: 1600px) {
  .chooseContainer {
    padding: 2rem;

    .chooseTitle {
      h3 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
      }

      h1 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
      }

      p {
        font-size: 1rem;
        margin-bottom: 0.5rem;
      }
    }

    .grid {
      gap: 1rem;
      grid-template-columns: repeat(3, 1fr);
      padding: 1rem 5rem;
    }

    .card {
      padding: 1rem;
      .icon {
        width: 4rem;
        height: 4rem;
      }

      h3 {
        font-size: 1.1rem;
      }

      p {
        font-size: 0.9rem;
      }
    }
  }
}

@media (max-width: 925px) {
  .chooseContainer {
    padding: 2rem 1rem;

    .grid {
      grid-template-columns: repeat(2, 1fr);
      padding: 1rem;
    }

    .card {
      padding: 0.5rem;
      .icon {
        width: 3rem;
        height: 3rem;
      }

      h3 {
        font-size: 1rem;
      }

      p {
        font-size: 0.9rem;
      }
    }
  }
}

@media (max-width: 680px) {
  .chooseContainer {
    padding: 2rem 1rem;

    .chooseTitle {
      h3 {
        font-size: 1.25rem;
      }

      h1 {
        font-size: 1.5rem;
      }

      p {
        font-size: 1rem;
      }
    }
    .grid {
      grid-template-columns: repeat(1, 1fr);
      padding: 1rem;
    }
  }
}
