.productDetailsContainer {
  max-width: 1200px;
  margin: 0 auto;
  margin-top: 6rem;
  padding: 2rem 1rem;
  min-height: 100vh;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  color: #666;

  a {
    color: #e53e3e;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  span:last-child {
    color: #333;
    font-weight: 500;
  }
}

.productMainSection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
  align-items: start;
}

.productImageSection {
  position: relative;
}

.mainImage {
  position: relative;
  width: 100%;
  height: 500px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.categoryBadge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 2;

  span {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;

    &.nonveg {
      background: #e53e3e;
    }

    &.veg {
      background: #38a169;
    }

    &.powder {
      background: #d69e2e;
    }
  }
}

.productInfoSection {
  padding: 1rem 0;
}

.productName {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.productTagline {
  font-size: 1.2rem;
  color: #666;
  font-style: italic;
  margin-bottom: 1.5rem;
}

.priceSection {
  margin-bottom: 2rem;
}

.price {
  font-size: 2rem;
  font-weight: 700;
  color: #e53e3e;
}

.description {
  margin-bottom: 2rem;

  h3 {
    font-size: 1.3rem;
    color: #2d3748;
    margin-bottom: 0.5rem;
  }

  p {
    line-height: 1.6;
    color: #4a5568;
  }
}

.caption {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  border-left: 4px solid #e53e3e;

  p {
    margin: 0;
    font-weight: 500;
    color: #2d3748;
  }
}

.actionButtons {
  display: flex;
  gap: 1rem;

  button {
    padding: 1rem 2rem;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
  }
}

.buyNowBtn {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(229, 62, 62, 0.3);
  }
}

.productDetailsSection {
  margin-bottom: 4rem;
}

.detailsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.detailCard {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

  h3 {
    font-size: 1.3rem;
    color: #2d3748;
    margin-bottom: 1rem;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
    color: #4a5568;

    &:last-child {
      border-bottom: none;
    }

    &:before {
      content: "•";
      color: #e53e3e;
      font-weight: bold;
      margin-right: 0.5rem;
    }
  }
}

.relatedProductsSection {
  h2 {
    font-size: 2rem;
    color: #2d3748;
    margin-bottom: 2rem;
    text-align: center;
  }
}

.relatedProductsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.relatedProductCard {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  text-decoration: none;
  color: inherit;

  &:hover {
    transform: translateY(-5px);
  }
}

.relatedProductImage {
  position: relative;
  width: 100%;
  height: 200px;
}

.relatedProductCard {
  h4 {
    padding: 1rem 1rem 0.5rem;
    font-size: 1.1rem;
    color: #2d3748;
    margin: 0;
  }

  p {
    padding: 0 1rem 1rem;
    color: #e53e3e;
    font-weight: 600;
    margin: 0;
  }
}

@media (max-width: 768px) {
  .productDetailsContainer {
    padding: 1rem;
  }

  .productMainSection {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .productName {
    font-size: 2rem;
  }

  .detailsGrid {
    grid-template-columns: 1fr;
  }

  .actionButtons {
    flex-direction: column;
  }

  .relatedProductsGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 650px) {
  .mainImage {
    position: relative;
    width: 20rem;
    height: 20rem;
    margin: 0 auto;
  }

  .productInfoSection {
    padding:1rem;
  }

  .productName {
    font-size: 1.75rem;
    margin-bottom: 0.3rem;
  }

  .productTagline {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }

  .priceSection {
    margin-bottom: 1rem;
  }

  .price {
    font-size: 1.25rem;
  }

  .description {
    margin-bottom: 1rem;

    h3 {
      font-size: 1.25rem;
      margin-bottom: 0.5rem;
    }

    p {
      line-height: 1.25;
    }
  }

  .caption {
    padding: 0.75rem;
    margin-bottom: 1rem;

    p {
      font-size: 0.85rem;
    }
  }

  .actionButtons {

    button {
      padding: 0.75rem 2rem;
      font-size: 1rem;
    }
  }
}
